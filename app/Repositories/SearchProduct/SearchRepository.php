<?php

namespace App\Repositories\SearchProduct;

use App\Helper\CacheHelper;
use App\Models\Filters;
use App\Models\Product;
use App\Models\ProductVisit;
use App\Models\User;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Repositories\BaseRepository;
use App\Repositories\SearchProduct\SearchRepositoryInterface;
use Aws\Exception\AwsException;
use Aws\PersonalizeRuntime\PersonalizeRuntimeClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Meilisearch\Endpoints\Indexes;
use ReflectionClass;
use Illuminate\Http\Response;

class SearchRepository extends BaseRepository implements SearchRepositoryInterface
{



    public $modelClass = Product::class;
    public $minutes = 10000;

    public function search(array $filters, int $page, int $limit, string $orderBy, string $query, int $filtersGroupsId): mixed
    {
        // Define relationships to be eager-loaded
        $with = [
            'covers',
            'variance' => function ($q) {
                $q->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $cacheHelper = new CacheHelper;


        $filters['isListed'] = true;

        $keys = $cacheHelper->remember(
            key: "attribute-keys",
            callback: function () {
                return resolve(AttributeRepositoryInterface::class)->getKeysFilterableAttributes();
            },
            tags: [],
            ttl: $this->minutes,
        );


        $facets = array_merge(Product::$filterableAttributes, $keys);



        list($orderBy, $orderDirection) = explode(',', $orderBy);

        // Build the search query
        $searchQuery = $this->modelClass::search($query, function (Indexes $meilisearch, $query, $options) use ($filters, $page, $limit, $facets) {
            // Set pagination and facet options
            $options['offset'] = ($page - 1) * $limit; // Calculate offset dynamically
            $options['limit'] = $limit;
            $options['facets'] = $facets;

            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        });

        if ('basePrice' == $orderBy) {
            $orderBy = 'minPrice';
        }


        $searchQuery->orderBy("hasStock", "DESC")->orderBy($orderBy, $orderDirection);

        $rawData = $searchQuery->raw();


        $filters = [];
        $products = $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->orderBy('score', 'DESC')->paginate(perPage: $limit, page: $page);




        $modelFilters = [];

        $keysFacetDistribution = array_keys($rawData['facetDistribution']);


        $filters = $cacheHelper->remember(
            key: "filters-attribute-$filtersGroupsId",
            callback: function () use ($keysFacetDistribution, $filtersGroupsId) {
                return \App\Models\Attribute::join('filters', 'filters.attributeId', '=', 'attributes.attributeId')
                    ->whereIn('attributes.key', $keysFacetDistribution)
                    ->where('filters.filtersGroupsId', $filtersGroupsId)
                    ->where('filters.filterType', 'attribute')
                    ->with(['options'])
                    ->get();
            },
            tags: [],
            ttl: $this->minutes,
        );

        foreach ($filters as $key => $filter) {
            $key = $filter->key;
            $class = 'App\Models\Filters\MeilisearchFilters\\AttributeFilter'::class;
            $modelFilters[$key] = new $class($filter);
            $modelFilters[$key]->setKey($key);
            if ($filter->filterType === 'attribute') {
                $modelFilters[$key]->setFacetsOptions($rawData['facetDistribution'][$filter->key]);
            }
        }

        $filter2 = $cacheHelper->remember(
            key: "filters-un-attribute-$filtersGroupsId",
            callback: function () use ($keysFacetDistribution, $filtersGroupsId) {
                return Filters::where('filtersGroupsId', $filtersGroupsId)->where('filterType', '!=', 'attribute')->get();
            },
            tags: [],
            ttl: $this->minutes,
        );

        foreach ($filter2 as $key => $f2) {
            $key = $f2->filterType;
            $class = 'App\Models\Filters\MeilisearchFilters\\' . str::ucfirst($f2->filterType) . 'Filter'::class;
            $modelFilters[$key] = new $class($f2);
            $modelFilters[$key]->setKey($key);


            if (in_array($f2->filterType, ['brandId', 'categories'])) {
                $modelFilters[$key]->setFacetsOptions($rawData['facetDistribution'][$f2->filterType]);
            }
        }

        // checked if count product less 0  return  404

        if ($products->count() === 0) {
            throw new \App\Exceptions\ModelNotFoundException(__('response-messages.not-found_products'), Response::HTTP_NOT_FOUND);
        }

        $products->meilisearchFilters = $modelFilters;
        $reflection = new ReflectionClass($products);
        $totalProperty = $reflection->getProperty('total');
        $totalProperty->setAccessible(true);
        $totalProperty->setValue($products, $rawData['nbHits']); // Override the total
        $perPageProperty = $reflection->getProperty('perPage');
        $perPageProperty->setAccessible(true);
        $perPageProperty->setValue($products, $limit); // Override the perPage

        return $products;
    }

    public function products(array $filters, int $page, int $limit, string $orderBy, string $query): mixed
    {
        // Define relationships to be eager-loaded
        $with = [
            'covers',
            'variance' => function ($q) {
                $q->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $cacheHelper = new CacheHelper;
        $filters['isListed'] = true;
        $filters['hasStock'] = true;
        $keys = $cacheHelper->remember(
            key: "attribute-keys",
            callback: function () {
                return resolve(AttributeRepositoryInterface::class)->getKeysFilterableAttributes();
            },
            tags: [],
            ttl: $this->minutes,
        );


        $facets = array_merge(Product::$filterableAttributes, $keys);



        list($orderBy, $orderDirection) = explode(',', $orderBy);

        // Build the search query
        $searchQuery = $this->modelClass::search($query, function (Indexes $meilisearch, $query, $options) use ($filters, $page, $limit, $facets) {
            // Set pagination and facet options
            $options['offset'] = ($page - 1) * $limit; // Calculate offset dynamically
            $options['limit'] = $limit;
            $options['facets'] = $facets;

            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        });

        if ('basePrice' == $orderBy) {
            $orderBy = 'minPrice';
        }

        $searchQuery->orderBy($orderBy, $orderDirection)->orderBy("hasStock", "DESC")->orderBy('score', 'DESC');


        $filters = [];
        $products = $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->orderBy('score', 'DESC')->orderBy("hasStock", "DESC")->paginate(perPage: $limit, page: $page);

        $rawData = $searchQuery->raw();

        $reflection = new ReflectionClass(objectOrClass: $products);
        $totalProperty = $reflection->getProperty('total');
        $totalProperty->setAccessible(true);
        $totalProperty->setValue($products, $rawData['nbHits']); // Override the total
        $perPageProperty = $reflection->getProperty('perPage');
        $perPageProperty->setAccessible(true);
        $perPageProperty->setValue($products, $limit); // Override the perPage

        return $products;
    }



    public function autocomplete(array $filters, int $limit, string $orderBy, string $query): mixed
    {

        $with = [
            'covers',
            'variance' => function ($q) {
                $q->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $filters['isListed'] = true;
        list($orderBy, $orderDirection) = explode(',', $orderBy);

        // Build the search query
        $searchQuery = $this->modelClass::search($query, function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {

            $options['limit'] = $limit;
            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        });


        if ('basePrice' == $orderBy) {
            $orderBy = 'minPrice';
        }


        $searchQuery->orderBy($orderBy, $orderDirection)->orderBy("hasStock", "DESC");


        $filters = [];
        $products = $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->take($limit)->orderBy('score', 'DESC')->get();


        return $products;


    }


    /*************  ✨ Windsurf Command ⭐  *************/
    /**
     * Applies filters to the options array for a search query.
     *
     * This method modifies the provided options array to include filters based on
     * the given filters array. It transforms the filters into a format suitable
     * for a search query, supporting both single and multiple values.
     *
     * @param array $options Reference to the options array that will be modified
     *                       to include filters. The 'filter' key is initialized if
     *                       not already present.
     * @param array $filters An associative array of filters where keys are the
     *                       filter names and values are the filter criteria.
     *                       Values can be single values or arrays of values.
     */

    /*******  d1d7c6a0-8edb-4bfb-8cd2-0584eda93604  *******/
    protected function applyFilters(array &$options, array $filters): void
    {
        if (!isset($options['filter'])) {
            $options['filter'] = [];
        }

        foreach ($filters as $key => $filter) {
            // Skip null or empty filters
            if ($filter === null || $filter === '' || (is_array($filter) && empty($filter))) {
                continue;
            }

            // Handle array filters (IN clause)
            if (is_array($filter)) {
                $this->applyArrayFilter($options, $key, $filter);
            }
            // Handle special price filter
            elseif ($key === 'price') {
                $this->applyPriceFilter($options, $filter);
            }
            // Handle Carbon date filters
            elseif ($key === 'createdAt') {
                $this->applyDateFilter($options, $key, $filter);
            } elseif ($key === 'numberOfOrder') {
                $this->applyNumberOfOrderFilter($options, $filter);
            } elseif ($key === 'viewCount') {
                $this->applyViewCountFilter($options, $filter);
            }
            // Handle comma-separated string values (convert to IN clause)
            elseif (is_string($filter) && strpos($filter, ',') !== false) {
                $this->applyCommaSeparatedFilter($options, $key, $filter);
            }
            // Handle single value filters
            else {
                $this->applySingleValueFilter($options, $key, $filter);
            }
        }
    }

    /**
     * Apply array filter as IN clause
     */
    private function applyArrayFilter(array &$options, string $key, array $filter): void
    {
        $filterValues = array_map(function ($value) {
            return $this->formatFilterValue($value);
        }, $filter);

        $options['filter'][] = "$key IN [" . implode(', ', $filterValues) . "]";
    }

    /**
     * Apply price range filter
     */
    private function applyPriceFilter(array &$options, string $filter): void
    {
        if (strpos($filter, ':') !== false) {
            list($min, $max) = explode(':', $filter);
            $min = (float) $min;
            $max = (float) $max;
            $options['filter'][] = "minPrice >= $min AND maxPrice <= $max";
        }
    }

    private function applyViewCountFilter(array &$options, string $filter): void
    {
        if (strpos($filter, ':') !== false) {
            list($min, $max) = explode(':', $filter);
            $min = (int) $min;
            $max = (int) $max;
            $options['filter'][] = "viewCount >= $min AND viewCount <= $max";
        }
    }

    private function applyNumberOfOrderFilter(array &$options, string $filter): void
    {
        if (strpos($filter, ':') !== false) {
            list($min, $max) = explode(':', $filter);
            $min = (int) $min;
            $max = (int) $max;
            $options['filter'][] = "numberOfOrder >= $min AND numberOfOrder <= $max";
        }
    }

    /**
     * Apply date filter for Carbon instances
     */
    private function applyDateFilter(array &$options, string $key, $filter): void
    {
        // Convert string date to timestamp if needed
        if (is_string($filter)) {
            $timestamp = strtotime($filter);
            $options['filter'][] = "$key < $timestamp";
        } elseif ($filter instanceof \Carbon\Carbon) {
            $options['filter'][] = "$key < $filter->timestamp";
        } else {

            $options['filter'][] = "$key < '$filter'";
        }
    }

    /**
     * Apply comma-separated string as IN clause
     */
    private function applyCommaSeparatedFilter(array &$options, string $key, string $filter): void
    {
        $values = array_map('trim', explode(',', $filter));
        $formattedValues = array_map(function ($value) {
            return $this->formatFilterValue($value);
        }, $values);

        $options['filter'][] = "$key IN [" . implode(', ', $formattedValues) . "]";
    }

    /**
     * Apply single value filter
     */
    private function applySingleValueFilter(array &$options, string $key, $filter): void
    {
        $filterValue = $this->formatFilterValue($filter);
        $options['filter'][] = "$key = $filterValue";
    }

    /**
     * Format filter value based on its type
     */
    private function formatFilterValue($value)
    {
        if (is_string($value) && !is_numeric($value)) {
            return "'$value'";
        } elseif (is_bool($value)) {
            return $value ? 'true' : 'false';
        } elseif (is_null($value)) {
            return 'null';
        } else {
            return $value;
        }
    }



    public function mostPopular($limit): mixed
    {
        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $filters = [
            'hasStock' => true,
            'isListed' => true,
            'viewCount' => '1:1000000',
            'price' => '50:1000',
        ];
        $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
            // Set pagination and facet options
            $options['limit'] = $limit;
            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        })->orderBy('numberOfOrder', 'DESC')->orderBy('score', 'DESC');


        return $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->get();


        $cacheHelper = new CacheHelper;
        return $cacheHelper->remember(
            key: "products-mostPopular_$limit",
            callback: function () use ($with, $limit) {
                $filters = [
                    'hasStock' => true,
                    'isListed' => true,
                    'numberOfOrder' => '1:1000000',
                    'price' => '50:1000',
                ];
                $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
                    // Set pagination and facet options
                    $options['limit'] = $limit;
                    $this->applyFilters($options, $filters);
                    return $meilisearch->search($query, $options);
                })->orderBy('numberOfOrder', 'DESC')->orderBy('score', 'DESC');


                return $searchQuery->query(function ($query) use ($with) {
                    $query->with($with);
                })->get();


            },
            tags: [],
            ttl: $this->minutes,
        );

    }


    public function newArrival($limit): mixed
    {
        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $cacheHelper = new CacheHelper;
        return $cacheHelper->remember(
            key: "products-newArrival-ml_$limit",
            callback: function () use ($with, $limit) {
                $filters = [
                    // 'createdAt' => (int) now()->subDays(value: 3)->timestamp,
                    'hasStock' => true,
                    'isListed' => true,
                    'price' => '50:1000',
                ];
                $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
                    $options['limit'] = $limit;
                    $this->applyFilters($options, $filters);
                    return $meilisearch->search($query, $options);
                })->orderBy('createdAt', 'DESC')
                    ->orderBy('score', 'DESC');

                return $searchQuery->query(function ($query) use ($with) {
                    $query->with($with);
                })->get();
            },
            tags: [],
            ttl: $this->minutes,
        );
    }



    public function offers($limit): mixed
    {

        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $filters = [
            'hasStock' => true,
            'isListed' => true,
            'hasOffer' => true,
        ];
        $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
            // Set pagination and facet options
            $options['limit'] = $limit;
            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        })->orderBy('score', 'DESC');


        return $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->get();


    }



    public function productsVisit($limit): mixed
    {
        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $modelName = null;
        if ($user = getUserCached()) {
            $modelName = $user->getMorphClass();
            $userId = $modelName === User::class ? "user_$user->userId" : "visitor_$user->visitorId";
        } else {
            $userId = "user_unknown";
        }

        $cacheHelper = new CacheHelper;

        return $cacheHelper->remember(
            key: "products-productsVisit_$userId" . "$limit",
            callback: function () use ($with, $limit, $user, $modelName) {
                if ($user) {
                    $productsId = $modelName === User::class ? ProductVisit::where('userId', $user->userId)->limit($limit)->pluck('productId') : ProductVisit::where('visitorId', $user->visitorId)->limit($limit)->pluck('productId');
                    return $this->modelClass::with($with)->where('isListed', true)->whereIn('productId', $productsId)->get();
                } else {
                    return $this->modelClass::with($with)->where('isListed', true)->where('hasStock', true)->orderBy('priority', 'DESC')->limit($limit)->get();
                }

            },
            tags: [],
            ttl: $this->minutes,
        );
    }









    public function suggestedProducts($productId, int $limit): mixed
    {

        $region = 'eu-west-1'; // Replace with your AWS region
        $personalizeArn = 'arn:aws:personalize:eu-west-1:574865621625:campaign/bought-together-campaign'; // Replace with your Campaign ARN.
        $filterArn = null;
        $promotionArn = null;
        $recommendedIds = [];
        $scores = [];
        $cacheHelper = new CacheHelper;

        return $cacheHelper->remember(
            key: "products-suggestedProducts_$productId",
            callback: function () use ($productId, $limit, $region, $personalizeArn, $filterArn, $promotionArn, &$recommendedIds, &$scores) {
                try {
                    $personalizeClient = new PersonalizeRuntimeClient([
                        'region' => $region,
                        'version' => 'latest',

                    ]);
                } catch (AwsException $e) {
                    throw new \Exception('Error creating Personalize client: ' . $e->getMessage());
                }

                $params = [
                    'campaignArn' => $personalizeArn,
                    'itemId' => "$productId",
                    'numResults' => $limit,
                    // ...existing code...
                ];

                try {
                    $result = $personalizeClient->getRecommendations($params);
                    if (!empty($result['itemList'])) {
                        foreach ($result['itemList'] as $item) {
                            $recommendedIds[] = $item['itemId'];
                            $scores[$item['itemId']] = $item['score'] ?? 0;
                        }
                    }
                } catch (AwsException $e) {
                    // ...existing code...
                }
                $with = [
                    'covers',
                    'variance' => function ($query) {
                        $query->where('isDefault', true);
                    },
                    'variance.activeStock',
                    'variance.media',
                    'activeStock'
                ];

                $filters = [
                    // 'hasStock' => true,
                    // 'isListed' => true,
                    'productId' => $recommendedIds,
                ];

                $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
                    // Set pagination and facet options
                    $options['limit'] = $limit;
                    $this->applyFilters($options, $filters);
                    return $meilisearch->search($query, $options);
                })->orderBy('score', 'DESC');

                return $searchQuery->query(function ($query) use ($with) {
                    $query->with($with);
                })->get();

            },
            tags: [],
            ttl: 60000,
        );



    }



}