<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;

class AddShippingForAllProductCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:shipping-for-all-product  {shippingId} {--brands=} {--categories=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add shipping carrier to products, optionally filtered by brands and categories';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $shippingId = $this->argument('shippingId');
        $brands = $this->option('brands') ?? null;
        $categories = $this->option('categories') ?? null;


        $this->info("Shipping ID: {$shippingId}");

        $this->info("Brands: {$brands}");

        $this->info("Categories: {$categories}");

        $query = Product::query();

        if ($brands) {
            $brandsArray = explode(',', $brands);
            $query->whereIn('brandId', $brandsArray);
        }

        if ($categories) {
            $categoriesArray = explode(',', $categories);
            $query->orWhereHas('categories', function ($q) use ($categoriesArray) {
                $q->whereIn('categories.categoryId', $categoriesArray);
            });
        }


        $products = $query->get();
        $this->info("Found {$products->count()} products to update");

        foreach ($products as $product) {
            // need add not sync
            $product->productShippings()->attach(['shippingCarrierId' => $shippingId]);
        }

        $this->info("Successfully attached shipping carrier {$shippingId} to {$products->count()} products");

        return 0;
    }
}