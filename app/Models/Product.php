<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;


use App\Enums\ProductTypeEnum;
use App\Models\Filters\ProductIdFilter;
use Carbon\Carbon;
use App\Traits\HasTranslations;
use App\Traits\Models\Lookable;
use App\Filters\PriceRangeFilter;
use App\Traits\Models\HasFilters;
use Spatie\MediaLibrary\HasMedia;
use App\Filters\IsPublishedFilter;
use App\Models\Filters\OrderByFilter;
use App\Models\Filters\CategoryIdFilter;
use Illuminate\Database\Eloquent\Collection;
use App\Traits\Models\AttributesOptionFilter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\Models\CustomInteractsWithMedia;
use App\Traits\Models\Searchable;
use Laravel\Scout\Searchable as SearchableMeilisearch;
use App\Filters\SearchFilter;
use App\Http\Resources\General\MediaResource;
use App\Models\Filters\MeilisearchFilters\BrandFilter;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Traits\Models\HasMeilisearchFilters;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * Class Product
 *
 * @property int $productId
 * @property array $name
 * @property int|null $categoryId
 * @property array $description
 * @property string $type
 * @property string $slug
 * @property bool $isPublished
 * @property bool $isListed
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 * @property string|null $deletedAt
 * @property Carbon|null $publishedAt
 * @property Carbon|null $unPublishedAt
 * @property Category|null $category
 * @property Alternative|null $alternative
 * @property Collection|Alternative[] $alternatives
 * @property Collection|Bundle[] $bundles
 * @property Collection|Cart[] $carts
 * @property Collection|OrderItem[] $orderItems
 * @property Collection|Attribute[] $attributes
 * @property Collection|ProductsMedia[] $productsMedia
 * @property Collection|RelatedProduct[] $relatedProducts
 * @property Collection|SuggestedProduct[] $suggestedProducts
 *
 * @package App\Models
 */
class Product extends BaseModel implements HasMedia
{
    use SoftDeletes, HasFactory, HasTranslations, Lookable, SearchableMeilisearch, Searchable, CustomInteractsWithMedia, HasMeilisearchFilters, HasFilters, AttributesOptionFilter;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    const DELETED_AT = 'deletedAt';
    protected $table = 'products';
    protected $primaryKey = 'productId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['name', 'description', 'metaTitle', 'metaDescription', 'oldSlug'];

    protected $casts = [
        'name' => 'json',
        'categoryId' => 'int',
        'description' => 'json',
        'slug' => 'string',
        'isPublished' => 'bool',
        'isListed' => 'bool',
        'avgRate' => 'float',
        'type' => "string",
        'SKU' => "string",
        'groupId' => 'int',
        'variationAttributes' => 'string',
        'priority' => 'int',
        'numberOfOrder' => 'int',
        'oldSlug' => 'json',
        'metaTitle' => 'json',
        'metaDescription' => 'json',
        'colors' => 'json',
        'minPrice' => 'float',
        'maxPrice' => 'float',
        'minPriceBeforeOffer' => 'float',
        'maxPriceBeforeOffer' => 'float',
        'hasStock' => 'bool',
        'hasOffer' => 'bool',
        'score' => 'float',
        'viewCount' => 'int'
    ];

    protected $dates = [
        'publishedAt',
        'unPublishedAt'
    ];
    protected $fillable = [
        'name',
        'categoryId',
        'description',
        'type',
        'slug',
        'isPublished',
        'isListed',
        'variationAttributes',
        'avgRate',
        'releaseAt',
        'priority',
        'numberOfOrder',
        'metaTitle',
        'metaDescription',
        'colors',
        'minPrice',
        'maxPrice',
        'minPriceBeforeOffer',
        'maxPriceBeforeOffer',
        'hasStock',
        'hasOffer',
        'score',
        'viewCount'
    ];

    public $imagesCollection = [
        'gallery',
        'cover'
    ];

    public static $sortableAttributes = [
        'productId',
        'name',
        'createdAt',
        'productId',
        'brandId',
        'avgRate',
        'score',
        'hasProductOffer',
        'maxPriceOffer',
        'minPriceOffer',
        'maxPrice',
        'minPrice',
        'numberOfOrder',
        'hasStock',
        'orderBy',
        'hasOffer',
        'basePrice',
        'viewCount',
        'SKU',
        'createdAt'
    ];
    public static $filterableAttributes = [
        'productId',
        'name',
        'categories',
        'brandId',
        'avgRate',
        'isListed',
        'score',
        'hasOffer',
        'maxPriceOffer',
        'minPriceOffer',
        'maxPrice',
        'minPrice',
        'numberOfOrder',
        'hasStock',
        'viewCount',
        'price',
        'SKU',
        'createdAt',
        'labels'

    ];



    protected static function booted()
    {

        Product::observe(\App\Observers\ProductObserver::class);
        parent::booted();
    }

    public function touchWithCalculation()
    {
        $this->touch();
        $product = self::setterMaxAndMinPrice($this);
        $product->searchable();
        return $this;
    }




    public static function setterMaxAndMinPrice($product)
    {
        // Avoid reloading relationships if they are already loaded
        $product->load([
            'variances',
            'variances.stocks',
            'variances.activeStock',
            'stocks',
            'activeStock'
        ]);

        $minPrice = 0;
        $maxPrice = 0;
        $minPriceBeforeOffer = 0;
        $maxPriceBeforeOffer = 0;
        $hasOffer = false;
        $hasStock = false;

        if ($product->type === ProductTypeEnum::bundle->value) {
            $latestStock = $product->stocks->sortByDesc('sort')->first();
            if ($latestStock) {
                $price = $latestStock->price;
                $priceBeforeOffer = $latestStock->priceBeforeOffer ?? $price;
                $minPrice = $price;
                $maxPrice = $price;
                $minPriceBeforeOffer = $priceBeforeOffer;
                $maxPriceBeforeOffer = $priceBeforeOffer;
            }
            $hasStock = !is_null($product->activeStock);
            $hasOffer = $product?->activeStock?->isOffer ?? false;
        } elseif ($product->type === ProductTypeEnum::alternative->value) {
            $product->variances->each(function ($variance) use (&$minPrice, &$maxPrice, &$hasStock, &$hasOffer, &$maxPriceBeforeOffer, &$minPriceBeforeOffer) {
                $latestStock = $variance->stocks->sortByDesc('sort')->first();
                if ($latestStock) {
                    $price = $latestStock->price;
                    $priceBeforeOffer = $latestStock->priceBeforeOffer ?? $price;
                    if ($minPrice === 0 || $price < $minPrice) {
                        $minPrice = $price;
                    }
                    if ($price > $maxPrice) {
                        $maxPrice = $price;
                    }
                    if ($minPriceBeforeOffer === 0 || $priceBeforeOffer < $minPriceBeforeOffer) {
                        $minPriceBeforeOffer = $priceBeforeOffer;
                    }
                    if ($priceBeforeOffer > $maxPriceBeforeOffer) {
                        $maxPriceBeforeOffer = $priceBeforeOffer;
                    }
                    if ($variance->activeStock?->isOffer) {
                        $hasOffer = true;
                    }
                }
                $hasStock = !is_null($variance->activeStock) || $hasStock;
            });
        } else {
            $latestStock = $product->variance?->stocks?->sortByDesc('sort')->first() ?? null;
            if ($latestStock) {
                $price = $latestStock->price;
                $minPrice = $price;
                $maxPrice = $price;
                $maxPriceBeforeOffer = $product?->variance?->activeStock?->priceBeforeOffer;
                $minPriceBeforeOffer = $product?->variance?->activeStock?->priceBeforeOffer;
            }
            $hasStock = $product?->variance?->activeStock ? true : false;
            $hasOffer = $product?->variance?->activeStock?->isOffer ? true : false;


        }

        $product->minPrice = $minPrice;
        $product->maxPrice = $maxPrice;
        $product->minPriceBeforeOffer = $minPriceBeforeOffer;
        $product->maxPriceBeforeOffer = $maxPriceBeforeOffer;
        $product->hasStock = $hasStock;
        $product->hasOffer = $hasOffer;


        $product->updateQuietly([
            'minPrice' => $minPrice,
            'maxPrice' => $maxPrice,
            'hasStock' => $hasStock,
            'hasOffer' => $hasOffer,
            'minPriceBeforeOffer' => $minPriceBeforeOffer,
            'maxPriceBeforeOffer' => $maxPriceBeforeOffer
        ]);


        return $product;
    }

    public function shouldBeSearchable(): bool
    {
        return is_null($this->deletedAt) && $this->isPublished() && $this->isListed;
    }


    public static function getFilterableAttributes()
    {
        return self::$filterableAttributes;
    }

    public static function getSortableAttributes()
    {
        return self::$sortableAttributes;
    }


    public function toSearchableArray()
    {

        $this->load('variances', 'variances.activeStock', 'activeStock', 'categories', 'labels', 'categories.labels', 'brand.labels');


        $this->translatable = [];
        $variances = [];
        $hasStock = false;
        $hasOffer = false;
        $attributesKeyValues = [];

        $productLabels = $this->labels->pluck('slug')->toArray();

        $this->brand->labels->each(function ($label) use (&$productLabels) {
            $productLabels[] = $label->slug;
        });

        $this->categories->each(function ($category) use (&$productLabels) {
            $category->labels->each(function ($label) use (&$productLabels) {
                $productLabels[] = $label->slug;
            });
        });
        /* calculate item score based on:
           1- product priority entered by admin, factor 10
           2- product avgRate, factor 20
           3- product publishedAt, the older the less score (it should not exceed 100 )
           4 - active stock 100 out of stock -100 for variance
           5 - have offer 100  for variance
        */
        $score = 0;
        $score += $this->priority ?? 0 * 25;

        $score += $this->avgRate * 5;

        $score += round(num: 50 - (Carbon::parse($this->publishedAt)->diffInDays(Carbon::now()) / 365) * 100);

        if ($this->type === ProductTypeEnum::bundle->value) {
            $isOffer = $this?->activeStock?->isOffer ?? false;
            $hasOffer = $this?->activeStock?->isOffer ? true : false;
            $hasStock = !is_null($this->activeStock) ? true : false;
            $basePrice = $this?->activeStock?->price ?? null;
        } else {

            foreach ($this->variances as $key => $variance) {
                $variance->translatable = [];
                $isOffer = $variance?->activeStock?->isOffer ?? false;
                $priceBeforeOffer = $isOffer ? $variance?->activeStock?->priceBeforeOffer : null;
                $hasOffer = $isOffer ? true : $hasOffer;
                $hasStock = !is_null($variance->activeStock) ? true : $hasStock;
                $basePrice = $variance?->activeStock?->price ?? null;

                $variances = [
                    'name' => [
                        'en' => $variance->name['en'],
                        'ar' => $variance->name['ar'],
                    ],
                    'basePrice' => $basePrice,
                    'priceBeforeOffer' => $priceBeforeOffer,
                    'isOffer' => $isOffer,
                    'SKU' => $variance->SKU,
                ];



                $variance->attributeValues->load(['attribute'])
                    ->whereNotNull('attributeOptionId')
                    ->each(function ($attributeValue) use (&$attributesKeyValues) {
                        $key = $attributeValue->attribute->key;
                        $optionId = $attributeValue->attributeOptionId;

                        if (!isset($attributesKeyValues[$key])) {
                            $attributesKeyValues[$key] = [];
                        }

                        if (!in_array($optionId, $attributesKeyValues[$key])) {
                            $attributesKeyValues[$key][] = $optionId;
                        }
                    });

            }

        }


        $this->attributeValues->load(['attribute'])
            ->whereNotNull('attributeOptionId')
            ->each(function ($attributeValue) use (&$attributesKeyValues) {
                $key = $attributeValue->attribute->key;
                $optionId = $attributeValue->attributeOptionId;

                if (!isset($attributesKeyValues[$key])) {
                    $attributesKeyValues[$key] = [];
                }

                if (!in_array($optionId, $attributesKeyValues[$key])) {
                    $attributesKeyValues[$key][] = $optionId;
                }
            });


        if ($hasOffer) {
            $score += 100;
        }

        if (!$hasStock) {
            $score = 0;
        }
        // need the last child in categories

        if ($this->categories->whereNotNull('parentId')->count() > 1) {
            $category = $this->categories->whereNotNull('parentId')->sortByDesc('parentId')->first();
            $score += $category->priority * 25;
        } else {
            $score += $this->categories->first()?->priority ?? 0 * 25;
        }

        $categories = $this->categories->pluck('categoryId')->toArray();

        $this->updateQuietly([
            'score' => $score
        ]);

        $result = [
            'productId' => $this->productId,
            'type' => $this->type,
            'name' => [
                'en' => $this->name['en'],
                'ar' => $this->name['ar'],
            ],
            'description' => [
                'en' => $this->description['en'],
                'ar' => $this->description['ar'],
            ],
            'SKU' => $this->SKU,
            // 'slug' => $this->slug,
            'isListed' => (bool) $this->isListed,
            'brandId' => (string) $this->brandId,
            'productPublishedAt' => (string) $this->publishedAt,
            'avgRate' => $this->avgRate,
            'createdAt' => (int) $this->createdAt->timestamp,
            "score" => $score,
            'variances' => $variances,
            'colors' => $this->colors,
            'categories' => $categories,
            'hasStock' => $this->hasStock,
            'maxPrice' => $this->maxPrice,
            'minPrice' => $this->minPrice,
            'hasOffer' => $this->hasOffer,
            'maxPriceBeforeOffer' => $this->maxPriceBeforeOffer,
            'minPriceBeforeOffer' => $this->minPriceBeforeOffer,
            "numberOfOrder" => $this->numberOfOrder,
            "labels" => $productLabels,
            'viewCount' => $this->viewCount,
        ];

        $result = array_merge($result, $attributesKeyValues);

        return $result;


    }







    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'brandId');
    }


    public function categories(): BelongsToMany
    {

        //! TODO: categories should be linked to variances not products. example: bundle product has watch and mobile
        return $this->belongsToMany(Category::class, 'categories_product', 'productId', 'categoryId')
            ->withPivot('categoryProductId')
            ->withTimestamps();
    }


    public function categoriesProduct(): BelongsToMany
    {
        return $this->belongsToMany(CategoryProduct::class, 'categories_product', 'productId', 'categoryId');
    }




    public function group(): BelongsTo
    {
        return $this->belongsTo(Groups::class, 'groupId');
    }

    public function alternatives(): HasMany
    {
        return $this->hasMany(Alternative::class, 'productId');
    }


    public function variances()
    {
        return $this->hasMany(Variance::class, 'productId');
        //  return $this->hasManyThrough(Variance::class, Alternative::class,'productId','varianceId','productId','productId');
    }


    public function variancesPublishing()
    {
        return $this->hasMany(Variance::class, 'productId')
            ->where('variances.isPublished', PUBLISHED)
            ->where(function ($query) {
                $query
                    ->orWhere(function ($query) {
                        $query->whereRaw('`variances`.`publishedAt` <= NOW()')
                            ->whereRaw('`variances`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereRaw('`variances`.`publishedAt` <= NOW()')
                            ->whereNull('variances.unPublishedAt');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('variances.publishedAt')
                            ->whereRaw('`variances`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('variances.publishedAt')
                            ->whereNull('variances.unPublishedAt');
                    });
            });
    }



    public function variationsAttributes()
    {

        return $this->hasManyThrough(
            VariancesAttribute::class,
            Variance::class,
            'productId',
            'varianceId',
            // Foreign key on Attribute table
            'productId',
            // Local key on Variance table
            'varianceId' // Local key on VariancesAttribute table
        )->join('attributes', 'attributes.attributeId', '=', 'variances_attributes.attributeId')
            ->join('attributes_values', 'attributes_values.attributeValuesId', '=', 'variances_attributes.attributeValuesId')
            ->join('attributes_options', 'attributes_options.attributeOptionId', '=', 'attributes_values.attributeOptionId')
            ->select(
                'attributes.*',
                'variances_attributes.*',
                'attributes_values.*',
                'attributes_options.name as option_name',
                'attributes_options.slug as option_slug',
                'attributes_options.hexCode as option_hexCode',
                'attributes_options.number as option_number'
            );
    }


    public function variance()
    {

        return $this->hasOne(Variance::class, 'productId');
        //return $this->hasOneThrough(Variance::class, Alternative::class,'productId','varianceId','productId','productId');
    }




    public function alternative()
    {
        return $this->hasOne(Alternative::class, 'productId')->where('alternatives.isDefault', 1);
    }

    public function default()
    {
        return $this->hasOne(Variance::class, 'productId');
        //return $this->hasOneThrough(Variance::class, Alternative::class,'productId','varianceId','productId','productId')->where('alternatives.isDefault', 1);
    }


    public function bundles(): HasMany
    {
        return $this->hasMany(Bundle::class, 'productId');
    }
    public function bundle()
    {
        return $this->hasOne(Bundle::class, 'productId');
    }


    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class, 'productId');
    }

    public function carts(): HasMany
    {
        return $this->hasMany(Cart::class, 'productId');
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'productId');
    }



    public function shippingCarriers(): BelongsToMany
    {
        return $this->belongsToMany(ShippingCarriers::class, 'product_shippings', 'productId', 'shippingCarrierId')
            ->withPivot('productShippingId')
            ->withTimestamps();
    }




    public function productShippings(): BelongsToMany
    {
        return $this->belongsToMany(ProductShipping::class, 'product_shippings', 'productId', 'shippingCarrierId');
    }





    public function wishlists()
    {
        return $this->belongsToMany(User::class, 'wishlists', 'productId', 'userId')->withPivot('wishlistId')->withTimestamps();
    }


    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'products_attributes', 'productId', 'attributeId')
            ->withPivot('productsAttributesId')
            ->withPivot('productId')
            ->withTimestamps();
    }


    public function attributesWithValue(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'products_attributes', 'productId', 'attributeId')
            ->join('attributes_values', function ($join) {
                $join->on('attributes_values.productId', '=', 'products_attributes.productId');
                $join->on('attributes_values.attributeId', '=', 'attributes.attributeId');
            })
            ->leftJoin('attributes_options', 'attributes_options.attributeOptionId', '=', 'attributes_values.attributeOptionId')
            ->select(
                'attributes.*',
                'attributes_values.*',

                //'attributes_options.*',

                'attributes_options.name as option_name',
                'attributes_options.slug as option_slug',
                'attributes_options.hexCode',
                'attributes_options.number',


                'products_attributes.productId as pivot_productId',
                'products_attributes.attributeId as pivot_attributeId',
                'products_attributes.productsAttributesId as  pivot_productsAttributesId',
                'products_attributes.createdAt as pivot_createdAt',
                'products_attributes.updatedAt as pivot_updatedAt'
            );
    }



    // public function productAttributes(): BelongsToMany
    // {
    //     return $this->belongsToMany(ProductsAttribute::class, 'products_attributes', 'productId', 'attributeId');
    // }

    public function productAttributes()
    {
        return $this->hasMany(ProductsAttribute::class, 'productId');
    }




    public function relatedProducts(): HasMany
    {
        return $this->hasMany(RelatedProduct::class, 'targetProductId');
    }

    public function related(): HasMany
    {
        return $this->hasMany(RelatedProduct::class, 'targetProductId');
    }


    public function suggestedProducts(): HasMany
    {
        return $this->hasMany(SuggestedProduct::class, 'targetProductId');
    }

    public function suggested(): HasMany
    {
        return $this->hasMany(SuggestedProduct::class, 'targetProductId');
    }

    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'productId',

        ];
    }


    public function allowedFilters(): array
    {
        return array_merge(
            $this->attributesFilter(),
            [
                'search' => SearchFilter::class,
                'isPublished' => IsPublishedFilter::class,
                'category' => CategoryIdFilter::class,
                // 'categories' => CategoryIdFilter::class,
                'priceRange' => PriceRangeFilter::class,
                // 'brand' => BrandFilter::class,
                'order_by' => OrderByFilter::class,
                'id' => ProductIdFilter::class,
            ]
        );
    }



    public function paymentsMethod(): BelongsToMany
    {
        return $this->belongsToMany(PaymentsMethod::class, 'product_payments', 'productId', 'paymentMethodId')
            ->withPivot('productPaymentId')
            ->withTimestamps();
    }


    public function disabledPaymentMethods(): BelongsToMany
    {
        return $this->belongsToMany(PaymentsMethod::class, 'product_payments', 'productId', 'paymentMethodId')
            ->withPivot('productPaymentId')
            ->withTimestamps();
    }


    public function productPayments(): HasMany
    {
        return $this->hasMany(ProductPayment::class, 'productId');
    }


    // public function attributeValues()
    // {
    //     return $this->hasMany(AttributesValue::class, 'productId');
    // }



    public function attributeValues()
    {
        return $this->hasMany(AttributesValue::class, 'productId');

    }



    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * @return array
     */
    // Get searchable attributes
    // The properties that should be searchable.
    // The array values should be the attribute names.
    // The array keys should be the relationship or accessor names.
    // For example:
    // [
    //     'title', // search in title attribute
    //     'content', // search in content attribute
    //     'comments.body', // search in comments relationship, body attribute
    //     'user.name', // search in user relationship, name attribute
    //     'translations.title', // search in translations relationship, title attribute
    //     'translations.content', // search in translations relationship, content attribute
    // ]
/******  3af92b24-4d5a-4284-acbc-33de49910bd8  *******/
    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'brand.name->ar',
            'brand.name->en',
            // 'description->ar',
            // 'description->en',
            // 'createdAt',
            // 'updatedAt',

        ];
    }



    public function productStocks(): BelongsToMany
    {
        return $this->belongsToMany(ProductStock::class, 'product_stocks', 'productId', 'stockId');
    }


    public function stocks(): BelongsToMany
    {
        return $this->belongsToMany(Stock::class, 'product_stocks', 'productId', 'stockId')
            ->withPivot('productStockId')
            ->withTimestamps();
    }

    public function activeStocks(): BelongsToMany
    {
        return $this->belongsToMany(Stock::class, 'product_stocks', 'productId', 'stockId')
            ->where('stocks.isPublished', PUBLISHED)
            ->where(function ($query) {
                $query
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereNull('stocks.unPublishedAt');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereNull('stocks.unPublishedAt');
                    });
            })
            ->where('stocks.quantity', '>', 0)
            ->where('stocks.quantity', '>', 'stocks.sold');

    }


    public function lastStock()
    {
        return $this->belongsTo(Stock::class, 'product_stocks', 'productId', 'stockId')
            ->orderBy('stocks.sort', 'asc');
    }


    public function activeStock()
    {
        // PLEASE DONT CHANGE
        return $this->hasOneThrough(Stock::class, ProductStock::class, 'productId', 'stockId', 'productId', 'stockId', )
            ->where('stocks.isPublished', PUBLISHED)
            ->where(function ($query) {
                $query
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereNull('stocks.unPublishedAt');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereNull('stocks.unPublishedAt');
                    });
            })
            ->where('stocks.quantity', '>', 0)
            ->where('stocks.quantity', '>', 'stocks.sold')
            ->orderBy('stocks.sort', 'asc');


    }

    public function productLabels(): HasMany
    {
        return $this->hasMany(ProductLabel::class, 'productId');
    }


    public function labels(): HasManyThrough
    {

        // return $this->belongsToMany(Label::class, 'product_labels', 'productId', 'labelId')
        //     ->withPivot('productLabelId')
        //     ->withPivot('productId')
        //     ->withTimestamps();


        return $this->hasManyThrough(
            Label::class,           // Final model
            ProductLabel::class,   // Intermediate model
            'productId',          // Foreign key on intermediate table
            'labelId',             // Foreign key on final table
            'productId',          // Local key on this table
            'labelId'              // Local key on intermediate table
        );
    }
}